class I18n:
    """国际化支持类"""
    
    def __init__(self, language='zh'):
        self.language = language
        self.translations = self._load_translations()
        
    def _load_translations(self):
        """加载翻译字典"""
        # 简单的中英文翻译字典示例
        translations = {
            'zh': {
                'app_title': 'AI助手',
                'chat': '对话',
                'translation': '翻译',
                'knowledge_base': '知识库',
                'term_base': '术语库',
                'settings': '设置',
                'file': '文件',
                'import_doc': '导入文档',
                'export_chat': '导出对话',
                'exit': '退出',
                'language': '语言',
                'help': '帮助',
                'about': '关于',
                'ready': '就绪',
                'language_changed': '语言已更改',
                'restart_to_apply': '需要重启应用以应用更改',
                'about_content': 'AI助手 v1.0\n一个功能强大的桌面AI助手',
                'model_settings': '模型设置',
                'ai_model_path': 'AI模型路径',
                'voice_model_path': '语音模型路径',
                'vector_model_path': '向量模型路径',
                'browse': '浏览',
                'hyperparameters': '超参数',
                'temperature': '温度',
                'max_length': '最大长度',
                'save_settings': '保存设置',
                'test_models': '测试模型',
                'persona': '人设',
                'ai_persona': 'AI人设',
                'settings_saved': '设置已保存',
                'type_message': '输入消息...',
                'use_knowledge_base': '使用知识库',
                'send': '发送',
                'clear_chat': '清空对话',
                'your_message': '你的消息',
                'source_language': '源语言',
                'target_language': '目标语言',
                'source_text': '原文',
                'use_term_base': '使用术语库',
                'import_file': '导入文件',
                'translate': '翻译',
                'translation_result': '翻译结果',
                'export_translation': '导出翻译',
                'select_file_to_translate': '选择要翻译的文件',
                'file_read_error': '文件读取错误',
                'save_translation': '保存翻译',
                'translation_saved': '翻译已保存',
                'save_error': '保存错误',
                'search': '搜索',
                'search_knowledge': '搜索知识库...',
                'search_term': '搜索术语...',
                'term': '术语',
                'definition': '定义',
                'import_terms': '导入术语表',
                'add_term': '添加术语',
                'save_term': '保存术语',
                'delete_term': '删除术语',
                'import_document': '导入文档',
                'add_knowledge': '添加知识',
                'save_knowledge': '保存知识',
                'delete_knowledge': '删除知识',
                'select_file_to_import': '选择要导入的文件',
                'import_success': '导入成功',
                'document_imported': '文档已导入',
                'import_error': '导入错误',
            },
            'en': {
                'app_title': 'AI Assistant',
                'chat': 'Chat',
                'translation': 'Translation',
                'knowledge_base': 'Knowledge Base',
                'term_base': 'Term Base',
                'settings': 'Settings',
                'file': 'File',
                'import_doc': 'Import Document',
                'export_chat': 'Export Chat',
                'exit': 'Exit',
                'language': 'Language',
                'help': 'Help',
                'about': 'About',
                'ready': 'Ready',
                'language_changed': 'Language Changed',
                'restart_to_apply': 'Restart required to apply changes',
                'about_content': 'AI Assistant v1.0\nA powerful desktop AI assistant',
                'model_settings': 'Model Settings',
                'ai_model_path': 'AI Model Path',
                'voice_model_path': 'Voice Model Path',
                'vector_model_path': 'Vector Model Path',
                'browse': 'Browse',
                'hyperparameters': 'Hyperparameters',
                'temperature': 'Temperature',
                'max_length': 'Max Length',
                'save_settings': 'Save Settings',
                'test_models': 'Test Models',
                'persona': 'Persona',
                'ai_persona': 'AI Persona',
                'settings_saved': 'Settings saved',
                'type_message': 'Type a message...',
                'use_knowledge_base': 'Use Knowledge Base',
                'send': 'Send',
                'clear_chat': 'Clear Chat',
                'your_message': 'Your Message',
                'source_language': 'Source Language',
                'target_language': 'Target Language',
                'source_text': 'Source Text',
                'use_term_base': 'Use Term Base',
                'import_file': 'Import File',
                'translate': 'Translate',
                'translation_result': 'Translation Result',
                'export_translation': 'Export Translation',
                'select_file_to_translate': 'Select File to Translate',
                'file_read_error': 'File Read Error',
                'save_translation': 'Save Translation',
                'translation_saved': 'Translation Saved',
                'save_error': 'Save Error',
                'search': 'Search',
                'search_knowledge': 'Search Knowledge...',
                'search_term': 'Search Term...',
                'term': 'Term',
                'definition': 'Definition',
                'import_terms': 'Import Terms',
                'add_term': 'Add Term',
                'save_term': 'Save Term',
                'delete_term': 'Delete Term',
                'import_document': 'Import Document',
                'add_knowledge': 'Add Knowledge',
                'save_knowledge': 'Save Knowledge',
                'delete_knowledge': 'Delete Knowledge',
                'select_file_to_import': 'Select File to Import',
                'import_success': 'Import Success',
                'document_imported': 'Document Imported',
                'import_error': 'Import Error',
            }
        }
        return translations
    
    def translate(self, key):
        """翻译指定键值"""
        if key not in self.translations.get(self.language, {}):
            # 如果找不到翻译，返回键名本身
            return key
            
        return self.translations[self.language][key]
    
    def set_language(self, language):
        """设置语言"""
        if language in self.translations:
            self.language = language 