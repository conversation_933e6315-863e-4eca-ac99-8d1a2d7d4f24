# VLLM 模型应用框架

这是一个基于VLLM（大型语言模型）技术的应用开发框架，旨在提供一套完整的工具和组件，用于快速构建和部署基于大语言模型的智能应用。

## 项目概述

本项目是一个高度模块化、可扩展的VLLM应用开发框架，包含以下核心功能：

- **模型加载与管理**：支持加载各种格式的大语言模型，包括Transformers模型和GGUF模型
- **向量数据库**：提供高效的文本向量存储和检索功能
- **知识库管理**：支持知识的导入、存储、检索和管理
- **应用打包**：支持将应用打包为可执行文件
- **版本管理**：提供版本控制和自动更新功能
- **资源打包**：支持将模型、数据等资源打包到应用中

## 系统架构

```
├── core/                    # 核心功能模块
│   ├── base_engine.py       # 基础引擎，提供模型加载等基础功能
│   ├── vector_db.py         # 向量数据库，提供文本向量存储和检索
│   ├── knowledge_base.py    # 知识库管理，提供知识的存储和检索
│   └── knowledge_engine.py  # 知识问答引擎，提供基于知识的问答功能
├── utils/                   # 工具函数模块
├── ui/                      # 用户界面模块
├── data/                    # 数据存储目录
├── config/                  # 配置文件目录
├── version_manager.py       # 版本管理工具
├── auto_updater.py          # 自动更新工具
├── build_exe.py             # 应用打包工具
└── AI_assistant.py          # 主应用程序
```

## 核心模块说明

### 1. 基础引擎 (base_engine.py)

基础引擎提供对大语言模型的加载和管理功能，主要特点：

- 支持多种模型格式（Transformers和GGUF）
- 自动优化模型加载参数，支持GPU加速
- 提供统一的模型推理接口

### 2. 向量数据库 (vector_db.py)

向量数据库负责文本向量的存储和检索，主要特点：

- 支持多种向量集合管理
- 高效的相似度搜索算法
- 支持向量持久化存储和加载

### 3. 知识库 (knowledge_base.py)

知识库模块管理结构化和非结构化知识，主要特点：

- 支持多种格式的知识导入（文本、PDF等）
- 自动文档解析和切分
- 基于向量数据库的高效检索

### 4. 知识问答引擎 (knowledge_engine.py)

知识问答引擎提供基于知识的问答功能，主要特点：

- 基于检索增强生成（RAG）技术
- 支持上下文管理和多轮对话
- 结果验证和质量控制

## 工具和集成

### 版本管理 (version_manager.py)

- 提供应用版本管理功能
- 支持远程版本检测和比较
- 生成增量更新包

### 自动更新 (auto_updater.py)

- 自动检测新版本
- 下载和应用更新
- 支持增量更新和完整更新

### 应用打包 (build_exe.py)

- 将应用打包为独立可执行文件
- 资源文件打包和管理
- 依赖项管理

## 快速开始

### 环境要求

- Python 3.8+ 
- CUDA 11.7+（如需GPU加速）
- 至少8GB内存，推荐16GB以上

### 安装步骤

1. 克隆仓库
```bash
git clone https://github.com/yourusername/vllm-application-framework.git
cd vllm-application-framework
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 创建数据目录
```bash
python create_data_dirs.py
```

4. 运行应用
```bash
python AI_assistant.py
```

## 开发指南

### 自定义模型

修改`config.json`文件中的`model_path`参数指向你的模型路径：

```json
{
  "model_path": "path/to/your/model",
  "temperature": 0.7,
  "max_new_tokens": 512
}
```

### 扩展知识库

通过代码或UI导入知识到知识库：

```python
from core.knowledge_base import KnowledgeBase

kb = KnowledgeBase()
kb.import_file("path/to/document.pdf")
```

### 创建自定义应用

继承核心模块创建自定义应用：

```python
from core.base_engine import BaseEngine
from core.knowledge_base import KnowledgeBase

class MyCustomApp:
    def __init__(self):
        self.engine = BaseEngine({"model_path": "path/to/model"})
        self.kb = KnowledgeBase()
        
    def run(self):
        # 自定义应用逻辑
        pass
```

## 技术栈

- **PyTorch**: 深度学习框架
- **Transformers**: 模型加载和推理
- **GGUF**: 轻量级模型格式支持
- **NumPy**: 数学计算
- **FastAPI**: API服务（可选）

## 贡献指南

1. Fork 仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

本项目采用 MIT 许可证 - 详情见 [LICENSE](LICENSE) 文件

## 联系方式

- 项目负责人: Your Name
- 邮箱: <EMAIL>
- 项目主页: https://github.com/yourusername/vllm-application-framework 

# AI助手 - 自动模型量化功能

## 功能简介

AI助手支持根据当前GPU资源自动选择模型量化级别，以便在各种硬件环境下获得最佳性能。量化可以减少模型所需的显存和内存，同时保持模型的推理能力。

## 核心功能特性

- **自动显存检测**：自动检测可用GPU显存，并根据实际情况选择合适的量化级别
- **多级量化支持**：支持FP16、INT8和INT4多种量化级别
- **特定模型优化**：针对Qwen等模型提供特定优化配置
- **内存使用优化**：提供显存动态分配和CPU卸载功能
- **可视化设置界面**：在设置面板中可直观地调整量化配置

## 量化级别说明

| 级别 | 描述 | 建议显存 | 性能影响 |
|------|------|----------|----------|
| NONE | 原始精度，不进行量化 | ≥24GB | 最佳性能，原始精度 |
| FP16 | 半精度浮点数 | ≥12GB | 轻微影响，几乎无精度损失 |
| INT8 | 8位整数量化 | ≥8GB | 中等影响，可能有少量精度损失 |
| INT4 | 4位整数量化 | ≥4GB | 显著影响，有一定精度损失 |

## 使用方法

1. 在设置面板中的"模型量化设置"组中选择量化选项
2. 可以选择"自动选择"让系统根据GPU资源自动决定量化级别
3. 如有特殊需要，可以手动选择特定的量化级别
4. 设置在下次加载模型时生效

## 技术实现

量化功能通过以下核心组件实现：

- **ModelQuantizer**：负责检测GPU资源并自动选择量化级别
- **BitsAndBytesConfig**：利用transformers的量化配置功能
- **显存管理**：根据不同硬件环境优化显存分配

## 支持的模型

目前自动量化功能已针对以下模型类型进行了优化：

- Qwen系列模型 (Qwen2-0.5B, Qwen2-1.5B, Qwen2-7B, Qwen1.5等)
- LLaMA系列模型
- 通用Transformer模型

## 注意事项

- INT4量化可能会对模型性能产生一定影响，主要体现在生成文本的质量和一致性上
- 使用较高级别的量化可能导致模型在某些知识密集型任务上表现降低
- 对于重要任务，建议在资源允许的情况下使用较低级别的量化或不进行量化

## 未来改进计划

- 添加更多模型专用优化配置
- 支持更多量化方法 (GPTQ, EETQ等)
- 动态运行时量化调整
- 性能监控和自动调整功能

# AI助手一体化打包工具

## 简介

AI助手一体化打包工具是一套为AI应用程序设计的打包解决方案，能够将AI助手应用与其环境（包括Python运行时、依赖库、模型文件等）一起打包成独立可执行文件或安装程序，便于分发和部署。

该工具集包含以下组件：

- **build_bundle.bat**: Windows批处理脚本，提供交互式界面，简化打包过程
- **package_app.py**: 基础打包脚本，适用于普通Python虚拟环境
- **package_conda_app.py**: 专为Conda环境设计的打包脚本，可捕获Conda环境中的依赖

## 功能特点

- 自动检测和捕获依赖关系
- 支持标准Python环境和Conda环境
- 可选择性包含大型模型文件
- 自动创建Windows安装程序
- 保留应用程序目录结构
- 提供简洁的命令行和交互式界面
- 生成详细的打包日志

## 系统要求

- Windows 10/11 (64位)
- Python 3.8+
- PyInstaller
- NSIS（可选，用于创建安装程序）

## 快速开始

### 使用交互式批处理脚本（推荐）

1. 双击运行 `build_bundle.bat`
2. 按照提示设置应用名称、版本号等选项
3. 确认设置并开始打包
4. 打包完成后，可执行文件将位于 `dist` 目录中

### 使用命令行（Python环境）

```bash
# 基本用法
python package_app.py

# 自定义应用名称和版本
python package_app.py --name "我的AI助手" --version "2.0.0"

# 包含模型文件
python package_app.py --include-models

# 不创建安装程序
python package_app.py --no-installer
```

### 使用命令行（Conda环境）

```bash
# 基本用法
python package_conda_app.py --conda-env myenv

# 自定义应用名称和版本，指定Conda环境
python package_conda_app.py --name "我的AI助手" --version "2.0.0" --conda-env myenv

# 包含模型文件
python package_conda_app.py --conda-env myenv --include-models
```

## 命令行参数

所有脚本支持以下通用参数：

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--name` | 应用程序名称 | "AI助手" |
| `--version` | 应用程序版本 | "1.0.0" |
| `--main` | 主脚本路径 | "AI_assistant.py" |
| `--output` | 输出目录 | "dist" |
| `--include-models` | 包含模型文件（大幅增加包体积） | False |
| `--no-installer` | 不创建安装程序 | False |

针对Conda环境的额外参数：

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--conda-env` | 指定Conda环境名称 | "vllm_env" |

## 打包流程

1. **环境检测**：检查Python/Conda环境，确保必要的依赖已安装
2. **依赖收集**：收集应用程序的依赖项
3. **数据文件收集**：收集配置文件、资源、模型文件等
4. **生成规格文件**：创建PyInstaller规格文件
5. **构建可执行文件**：使用PyInstaller构建独立可执行文件
6. **创建安装程序**：使用NSIS创建Windows安装程序（可选）

## 目录结构

打包后的应用程序将保留原有目录结构：

```
AI助手/
├── AI助手.exe        # 主可执行文件
├── app_info.json     # 应用程序信息
├── config.json       # 配置文件
├── README.md         # 说明文档
├── data/             # 数据目录
│   ├── knowledge/    # 知识库
│   ├── terms/        # 术语库
│   └── vectors/      # 向量存储
├── models/           # 模型文件（如果包含）
└── python-XXX/       # Python运行时
```

## 注意事项

1. 打包过程可能需要较长时间，特别是包含模型文件时
2. 打包后的应用体积较大，建议在分发前进行测试
3. 对于特别大的模型文件（如完整的LLM模型），建议单独分发
4. 部分依赖可能需要特殊处理，请参阅日志输出
5. 创建安装程序需要安装NSIS并将其添加到PATH环境变量

## 常见问题

**Q: 打包失败，出现"No module named XXX"错误**  
A: 这通常是由于某些隐藏依赖未被正确捕获。请检查日志，手动将缺失的依赖添加到requirements.txt中，然后重新打包。

**Q: 打包成功但运行时报错**  
A: 可能是由于某些资源文件路径问题。请检查应用程序是否正确处理了资源文件路径。使用相对路径或应用内部路径解析机制。

**Q: 打包后的文件太大**  
A: 考虑以下措施减小体积：
- 不包含模型文件，改为运行时下载
- 使用更轻量级的依赖库
- 清理不必要的数据文件

**Q: 在非Windows系统上使用**  
A: package_app.py 和 package_conda_app.py 支持在Linux和macOS上运行，但创建安装程序功能仅适用于Windows。 